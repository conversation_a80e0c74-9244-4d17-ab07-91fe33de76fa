import akshare as ak
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def calculate_deviation(stock_code, index_code, days):
    """
    计算指定天数内个股相对于指数的偏离值
    """
    try:
        # 1. 获取股票历史数据
        stock_df = ak.stock_zh_a_hist(symbol=stock_code, period="daily", adjust="qfq")
        stock_df['日期'] = pd.to_datetime(stock_df['日期'])
        stock_df.set_index('日期', inplace=True)

        # 2. 获取指数历史数据
        index_df = ak.stock_zh_a_hist(symbol=index_code, period="daily", adjust="")
        index_df['日期'] = pd.to_datetime(index_df['日期'])
        index_df.set_index('日期', inplace=True)
        index_df.rename(columns={'收盘': 'index_close'}, inplace=True)

        # 3. 合并数据
        merged = pd.merge(stock_df[['收盘']],
                          index_df[['index_close']],
                          left_index=True,
                          right_index=True,
                          how='inner')

        if len(merged) < days:
            return None, f"数据不足，仅有{len(merged)}个交易日数据"

        # 4. 计算偏离值 ……（后续代码保持不变）
        stock_start = merged['收盘'].iloc[-days]
        stock_end   = merged['收盘'].iloc[-1]
        stock_return = (stock_end - stock_start) / stock_start * 100

        index_start = merged['index_close'].iloc[-days]
        index_end   = merged['index_close'].iloc[-1]
        index_return = (index_end - index_start) / index_start * 100

        deviation = stock_return - index_return
        return deviation, None

    except Exception as e:
        return None, f"计算错误: {str(e)}"

def get_stock_market(stock_code):
    """确定股票所属市场和参考指数"""
    if stock_code.startswith('60'):
        return 'sh', '上证指数', '000001'
    elif stock_code.startswith('688'):
        return 'sh', '上证指数', '000001'
    elif stock_code.startswith('00'):
        return 'sz', '深证A指', '399107'
    elif stock_code.startswith('30'):
        return 'sz', '创业板综指', '399102'
    else:
        return None, None, None

def check_abnormal_fluctuation(stock_code):
    """主函数：检查股票异动情况"""
    # 获取股票市场信息
    exchange, index_name, index_code = get_stock_market(stock_code)
    if not exchange:
        return "错误：不支持的股票代码格式"
    
    # 确定板块类型
    if stock_code.startswith('688') or stock_code.startswith('30'):
        board_type = '科创板/创业板'
        abnormal_threshold = 30  # 3日偏离30%为普通异动
        max_abnormal_count = 3   # 10日内3次同向异动触发严重异动
    else:
        board_type = '主板'
        abnormal_threshold = 20  # 3日偏离20%为普通异动
        max_abnormal_count = 4   # 10日内4次同向异动触发严重异动
    
    print(f"股票代码: {stock_code} | 市场: {'上交所' if exchange == 'sh' else '深交所'}")
    print(f"参考指数: {index_name}({index_code}) | 板块: {board_type}")
    print("-" * 60)
    
    # 计算10日偏离值
    dev_10d, error = calculate_deviation(stock_code, index_code, 10)
    if error:
        return error
    
    # 计算30日偏离值
    dev_30d, error = calculate_deviation(stock_code, index_code, 30)
    if error:
        return error
    
    # 计算最近10个交易日的3日偏离值（用于统计异动次数）
    # 这里简化处理，实际应该计算每个交易日的3日偏离值
    # 目前先用单次3日偏离值作为示例
    dev_3d, _ = calculate_deviation(stock_code, index_code, 3)
    dev_3d_list = [dev_3d] if dev_3d is not None else []
    
    # 统计普通异动次数
    up_count = sum(1 for dev in dev_3d_list if dev >= abnormal_threshold)
    down_count = sum(1 for dev in dev_3d_list if dev <= -abnormal_threshold)
    
    # 计算距离严重异动的空间
    space_10d_up = max(0, 100 - dev_10d) if dev_10d < 100 else 0
    space_10d_down = max(0, abs(dev_10d) - 50) if dev_10d > -50 else 0
    
    space_30d_up = max(0, 200 - dev_30d) if dev_30d < 200 else 0
    space_30d_down = max(0, abs(dev_30d) - 70) if dev_30d > -70 else 0
    
    space_count_up = max(0, max_abnormal_count - up_count)
    space_count_down = max(0, max_abnormal_count - down_count)
    
    # 输出结果
    result = f"""
>>> 严重异动空间分析 <<<

1. 10日偏离值: {dev_10d:.2f}%
   • 距+100%空间: {space_10d_up:.2f}%
   • 距-50%空间: {space_10d_down:.2f}%

2. 30日偏离值: {dev_30d:.2f}%
   • 距+200%空间: {space_30d_up:.2f}%
   • 距-70%空间: {space_30d_down:.2f}%

3. 最近10日普通异动次数:
   • 上涨异动: {up_count}次 (阈值: {abnormal_threshold}%↑)
   • 下跌异动: {down_count}次 (阈值: {abnormal_threshold}%↓)
   • 距触发条件: 
        {space_count_up}次上涨异动 (需{max_abnormal_count}次)
        {space_count_down}次下跌异动 (需{max_abnormal_count}次)
"""
    return result

# 使用示例
if __name__ == "__main__":
    stock_code = input("请输入A股股票代码(例如: 600000): ").strip()
    print(check_abnormal_fluctuation(stock_code))