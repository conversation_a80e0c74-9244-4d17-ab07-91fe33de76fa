# -*- coding: utf-8 -*-
"""
pip install akshare pandas tqdm -i https://pypi.tuna.tsinghua.edu.cn/simple
"""
import akshare as ak
import pandas as pd
from tqdm import tqdm

# -------------------------------------------------
# 1. 拉取概念列表
# -------------------------------------------------
concept_df = ak.stock_board_concept_name_ths()
print("实际列名：", list(concept_df.columns))
print(concept_df.head())          # 确认列顺序

# 根据实际列名直接取值
names = concept_df['name'].tolist()
codes = concept_df['code'].tolist()

# -------------------------------------------------
# 2. 逐条拉取概念指数日线
# -------------------------------------------------
all_close = {}

for name, code in tqdm(zip(names, codes), total=len(names), desc="拉取概念"):
    try:
        hist = ak.stock_board_concept_hist_ths(symbol=name, start_year="2024")
        hist['日期'] = pd.to_datetime(hist['日期'])
        close = hist.set_index('日期')['收盘'].sort_index()
        all_close[name] = close
    except Exception as e:
        # 网络/解析异常直接跳过
        continue

# -------------------------------------------------
# 3. 计算动量 & 输出
# -------------------------------------------------
panel = pd.DataFrame(all_close).tail(120)   # 最近约半年

def momentum_rank(df, windows=[1, 5, 20]):
    res = {}
    for w in windows:
        res[f'{w}日收益%'] = ((df / df.shift(w) - 1) * 100).iloc[-1].round(2)
    return pd.DataFrame(res)

rank = momentum_rank(panel).sort_values(by='20日收益%', ascending=False)

print("\n>>> 近 20 日热点题材 Top-20")
print(rank.head(20))