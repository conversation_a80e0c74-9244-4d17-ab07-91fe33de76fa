import akshare as ak
import pandas as pd
import time
import numpy as np
import requests
from functools import partial
import os
import json
import datetime

# 设置全局超时时间
session = requests.Session()
session.request = partial(session.request, timeout=30)
ak.session = session

# 缓存文件路径
CACHE_DIR = "akshare_cache"
os.makedirs(CACHE_DIR, exist_ok=True)

def get_cached_data(cache_key, max_age_hours=24):
    """获取缓存数据"""
    cache_file = os.path.join(CACHE_DIR, f"{cache_key}.json")
    if os.path.exists(cache_file):
        file_age = time.time() - os.path.getmtime(cache_file)
        if file_age < max_age_hours * 3600:  # 24小时内的缓存有效
            with open(cache_file, "r", encoding="utf-8") as f:
                try:
                    return json.load(f)
                except:
                    pass
    return None

def save_to_cache(cache_key, data):
    """保存数据到缓存"""
    cache_file = os.path.join(CACHE_DIR, f"{cache_key}.json")
    with open(cache_file, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def safe_get_sector_stocks(sector_name, max_retries=5):
    """安全获取板块成分股，带重试机制和缓存"""
    # 尝试多种可能的名称组合
    possible_names = [
        sector_name,
        sector_name + "概念",
        sector_name + "板块",
        "工业" + sector_name,
        sector_name + "设备",
        sector_name + "技术",
        sector_name + "产业",
        sector_name + "制造"
    ]
    
    # 先尝试从缓存获取
    cache_key = f"sector_{sector_name}"
    cached_data = get_cached_data(cache_key)
    if cached_data:
        print(f"使用缓存数据: {sector_name}板块 ({len(cached_data)}只股票)")
        return pd.DataFrame(cached_data)
    
    for name in possible_names:
        for attempt in range(max_retries):
            # 尝试作为行业板块获取
            try:
                df = ak.stock_board_industry_cons_em(symbol=name)
                if not df.empty:
                    print(f"作为行业板块获取到{len(df)}只成分股 (名称: {name})")
                    # 保存到缓存
                    save_to_cache(cache_key, df.to_dict(orient="records"))
                    return df
            except Exception as e:
                # 行业板块获取失败，继续尝试概念板块
                pass

            # 尝试作为概念板块获取
            try:
                df = ak.stock_board_concept_cons_em(symbol=name)
                if not df.empty:
                    print(f"作为概念板块获取到{len(df)}只成分股 (名称: {name})")
                    # 保存到缓存
                    save_to_cache(cache_key, df.to_dict(orient="records"))
                    return df
            except Exception as e:
                print(f"第{attempt+1}次尝试获取板块'{name}'失败: {str(e)}")

            time.sleep(1)  # 每次重试前等待1秒
    
    # 尝试获取所有概念板块，然后匹配最接近的名称
    print("尝试在所有概念板块中匹配相似名称...")
    concept_names = safe_get_concept_names()
    best_match = None
    best_score = 0
    
    for concept in concept_names:
        # 计算名称相似度
        score = sum(1 for char in sector_name if char in concept)
        if score > best_score:
            best_score = score
            best_match = concept
    
    if best_match:
        print(f"找到相似板块: '{best_match}'，尝试获取...")
        for attempt in range(max_retries):
            try:
                df = ak.stock_board_concept_cons_em(symbol=best_match)
                if not df.empty:
                    print(f"成功获取{len(df)}只成分股 (相似名称: {best_match})")
                    # 保存到缓存
                    save_to_cache(cache_key, df.to_dict(orient="records"))
                    return df
            except Exception as e:
                print(f"获取相似板块失败: {str(e)}")
            time.sleep(1)
    
    return pd.DataFrame()  # 返回空DataFrame

def safe_get_market_data(max_retries=5):
    """安全获取市场数据，带重试机制和缓存"""
    # 先尝试从缓存获取
    today = datetime.datetime.now().strftime("%Y%m%d")
    cache_key = f"market_data_{today}"
    cached_data = get_cached_data(cache_key)
    if cached_data:
        print(f"使用缓存市场数据 ({len(cached_data)}只股票)")
        return pd.DataFrame(cached_data)[['代码', '总市值', '成交量']]
    
    for attempt in range(max_retries):
        try:
            print(f"获取市场数据(尝试{attempt+1}/{max_retries})...")
            market_data = ak.stock_zh_a_spot_em()
            if not market_data.empty:
                print(f"成功获取{len(market_data)}只股票数据")
                # 保存到缓存
                save_to_cache(cache_key, market_data.to_dict(orient="records"))
                return market_data[['代码', '总市值', '成交量']]
        except Exception as e:
            print(f"获取市场数据失败: {str(e)}")
        time.sleep(2)  # 每次重试前等待2秒
    
    # 尝试从昨天的缓存获取
    yesterday = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime("%Y%m%d")
    cache_key = f"market_data_{yesterday}"
    cached_data = get_cached_data(cache_key, max_age_hours=48)  # 48小时内有效
    if cached_data:
        print(f"⚠️ 使用昨日缓存市场数据 ({len(cached_data)}只股票)")
        return pd.DataFrame(cached_data)[['代码', '总市值', '成交量']]
    
    return pd.DataFrame(columns=['代码', '总市值', '成交量'])

def safe_get_concept_names(max_retries=3):
    """安全获取所有概念板块名称，带缓存"""
    cache_key = "concept_names"
    cached_data = get_cached_data(cache_key, max_age_hours=72)
    if cached_data:
        print(f"使用缓存的概念板块列表 ({len(cached_data)}个)")
        return cached_data
    
    concept_names = []
    for attempt in range(max_retries):
        try:
            concept_df = ak.stock_board_concept_name_em()
            if not concept_df.empty:
                concept_names = concept_df['板块名称'].tolist()
                print(f"获取概念板块: {len(concept_names)}个")
                save_to_cache(cache_key, concept_names)
                return concept_names
        except Exception as e:
            print(f"获取概念板块失败(尝试{attempt+1}): {e}")
        time.sleep(2)
    
    return []  # 返回空列表

def safe_get_industry_names(max_retries=3):
    """安全获取所有行业板块名称，带缓存"""
    cache_key = "industry_names"
    cached_data = get_cached_data(cache_key, max_age_hours=72)
    if cached_data:
        print(f"使用缓存的行业板块列表 ({len(cached_data)}个)")
        return cached_data
    
    industry_names = []
    for attempt in range(max_retries):
        try:
            industry_df = ak.stock_board_industry_name_em()
            if not industry_df.empty:
                industry_names = industry_df['板块名称'].tolist()
                print(f"获取行业板块: {len(industry_names)}个")
                save_to_cache(cache_key, industry_names)
                return industry_names
        except Exception as e:
            print(f"获取行业板块失败(尝试{attempt+1}): {e}")
        time.sleep(2)
    
    return []  # 返回空列表

def get_sector_leader(sector_name, top_n=5):
    """
    获取指定板块的龙头中军股票
    :param sector_name: 板块名称（如"半导体"、"算力概念"）
    :param top_n: 返回前N名龙头股
    :return: DataFrame包含龙头股代码、名称、市值、成交量
    """
    print(f"\n开始获取'{sector_name}'板块成分股...")
    sector_stocks = safe_get_sector_stocks(sector_name)
    if sector_stocks.empty:
        print(f"⚠️ 无法获取'{sector_name}'板块成分股")
        
        # 提供可能的替代名称建议
        concept_names = safe_get_concept_names()
        industry_names = safe_get_industry_names()
        all_names = concept_names + industry_names
        
        similar_names = [name for name in all_names if sector_name in name]
        if similar_names:
            print(f"可能的相关板块: {', '.join(similar_names[:5])}")
        else:
            # 使用模糊匹配
            print("尝试模糊匹配...")
            for name in all_names:
                if sector_name in name or any(char in name for char in sector_name):
                    similar_names.append(name)
            
            if similar_names:
                print(f"可能的匹配板块: {', '.join(similar_names[:5])}")
        
        return None
    
    # 统一列名处理
    if '代码' not in sector_stocks.columns and 'symbol' in sector_stocks.columns:
        sector_stocks.rename(columns={'symbol': '代码', 'name': '名称'}, inplace=True)
    
    print(f"成功获取{len(sector_stocks)}只成分股，开始获取实时行情数据...")
    market_data = safe_get_market_data()
    
    # 合并板块数据与行情数据
    merged_data = pd.merge(
        sector_stocks[['代码', '名称']],
        market_data,
        on='代码',
        how='left'
    )
    
    # 如果合并后没有数据，返回空
    if merged_data.empty:
        print("⚠️ 合并后无数据")
        return None
    
    # 处理数值列
    merged_data['总市值'] = pd.to_numeric(merged_data['总市值'], errors='coerce')
    merged_data['成交量'] = pd.to_numeric(merged_data['成交量'], errors='coerce')
    
    # 处理缺失值 - 使用中位数填充
    market_cap_median = merged_data['总市值'].median()
    volume_median = merged_data['成交量'].median()
    merged_data['总市值'] = merged_data['总市值'].fillna(market_cap_median)
    merged_data['成交量'] = merged_data['成交量'].fillna(volume_median)
    
    # 计算龙头中军指标
    merged_data['总市值_归一化'] = merged_data['总市值'] / merged_data['总市值'].max()
    merged_data['成交量_归一化'] = merged_data['成交量'] / merged_data['成交量'].max()
    merged_data['龙头得分'] = 0.7 * merged_data['总市值_归一化'] + 0.3 * merged_data['成交量_归一化']
    
    # 按得分排序取前N名
    leaders = merged_data.sort_values('龙头得分', ascending=False).head(top_n)
    return leaders[['代码', '名称', '总市值', '成交量', '龙头得分']]

def safe_get_all_sector_names(max_retries=3):
    """安全获取所有板块名称，带缓存"""
    cache_key = "all_sectors"
    cached_data = get_cached_data(cache_key, max_age_hours=72)
    if cached_data:
        print(f"使用缓存板块列表 ({len(cached_data)}个板块)")
        return cached_data
    
    # 获取行业板块
    industry_names = safe_get_industry_names()
    
    # 获取概念板块
    concept_names = safe_get_concept_names()
    
    # 合并板块
    all_sectors = industry_names + concept_names
    
    # 去重并排序
    all_sectors = sorted(set(all_sectors))
    
    # 保存到缓存
    save_to_cache(cache_key, all_sectors)
    
    return all_sectors

# 使用示例
if __name__ == "__main__":
    print("="*50)
    print("板块龙头中军分析工具")
    print("="*50)
    print(f"缓存目录: {os.path.abspath(CACHE_DIR)}")
    
    # 获取所有板块名称
    sectors = safe_get_all_sector_names()
    print(f"\n可用板块总数: {len(sectors)}")
    
    # 用户输入板块名称
    while True:
        print("\n="*50)
        print("提示：输入'list'查看所有板块，输入'search 关键词'搜索板块")
        print("="*50)
        user_input = input("\n请输入要分析的板块名称 (输入q退出): ").strip()
        
        if user_input.lower() == 'q':
            break
            
        # 查看所有板块
        if user_input.lower() == 'list':
            print("\n所有板块列表:")
            for i, sector in enumerate(sectors):
                print(f"{i+1}. {sector}", end="\t")
                if (i+1) % 5 == 0:
                    print()
            continue
            
        # 搜索板块
        if user_input.lower().startswith('search '):
            keyword = user_input[7:].strip()
            print(f"\n搜索板块: '{keyword}'")
            found = [s for s in sectors if keyword in s]
            if found:
                print(f"找到 {len(found)} 个相关板块:")
                for i, sector in enumerate(found):
                    print(f"{i+1}. {sector}")
            else:
                print("未找到相关板块")
            continue
            
        sector = user_input
        print(f"\n开始分析'{sector}'板块的龙头中军...")
        leaders = get_sector_leader(sector)
        
        if leaders is not None and not leaders.empty:
            print(f"\n🔝 {sector}板块龙头中军TOP {len(leaders)}:")
            print("-"*60)
            # 格式化输出
            formatted = leaders.copy()
            formatted['总市值(亿)'] = formatted['总市值'] / 1e8
            formatted['成交量(万手)'] = formatted['成交量'] / 10000
            print(formatted[['代码', '名称', '总市值(亿)', '成交量(万手)', '龙头得分']].to_string(index=False, float_format="%.2f"))
            print("-"*60)
            
            # 简单分析
            print("\n📊 分析结果:")
            print(f"1. 龙头股: {leaders.iloc[0]['名称']}({leaders.iloc[0]['代码']})")
            print(f"   - 龙头得分: {leaders.iloc[0]['龙头得分']:.4f}")
            print(f"   - 总市值: {leaders.iloc[0]['总市值']/1e8:.2f}亿元")
            print(f"   - 成交量: {leaders.iloc[0]['成交量']/10000:.2f}万手")
            
            # 计算市值占比
            total_market_cap = leaders['总市值'].sum()
            if total_market_cap > 0:
                market_share = leaders.iloc[0]['总市值'] / total_market_cap * 100
                print(f"   - 板块市值占比: {market_share:.1f}%")
        else:
            print("\n⚠️ 未能获取该板块的龙头股信息，请尝试其他板块名称")