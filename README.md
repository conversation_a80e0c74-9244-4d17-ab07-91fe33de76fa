# 📈 A股量化分析工具集

一套基于Python的A股市场量化分析工具，提供板块龙头分析、异动检测、热点题材挖掘等功能。

## ✨ 功能特色

### 🎯 龙头中军分析 (`龙头中军.py`)
- **智能板块识别**：支持521个行业板块和概念板块
- **龙头股票筛选**：基于市值和成交活跃度的综合评分
- **概念板块优化**：针对数字货币、AI等概念板块，更重视成交活跃度
- **实时数据缓存**：提高查询效率，减少API调用

### 📊 严重异动计算 (`严重异动计算.py`)
- **偏离度分析**：计算个股相对板块指数的偏离程度
- **多时间周期**：支持3日、10日、30日等不同周期分析
- **异动空间评估**：评估股票的异动潜力和风险

### 🔥 热点题材挖掘 (`近期热点题材.py`)
- **题材热度排名**：基于涨跌幅和成交量的综合评分
- **板块轮动分析**：识别当前市场热点和资金流向

### 📉 回调分析 (`近期没有回调完毕的股票.py`)
- **回调检测**：识别尚未完成回调的强势股票
- **趋势分析**：评估股票的技术面强弱

## 🚀 快速开始

### 环境要求
- Python 3.7+
- 依赖包：akshare, pandas, numpy, tqdm

### 安装依赖
```bash
pip install akshare pandas numpy tqdm
```

### 使用示例

#### 1. 龙头中军分析
```bash
python 龙头中军.py
```
- 输入板块名称（如：半导体、数字货币、新能源）
- 支持模糊搜索：`search 电池`
- 查看所有板块：`list`

#### 2. 异动分析
```bash
python 严重异动计算.py
```
- 输入股票代码（如：600000、002594）
- 自动识别所属板块和对应指数
- 计算多周期偏离度

#### 3. 热点挖掘
```bash
python 近期热点题材.py
```

## 📊 核心算法

### 龙头股评分算法
```
概念板块：龙头得分 = 20% × 市值权重 + 80% × 成交额权重
传统板块：龙头得分 = 70% × 市值权重 + 30% × 成交额权重
```

**设计理念**：
- 概念板块更重视市场关注度（成交活跃度）
- 传统行业更重视企业规模（市值稳定性）
- 使用对数变换减少异常值影响

### 异动检测算法
```
偏离度 = (个股涨跌幅 - 指数涨跌幅) × 100%
```

## 🎯 使用场景

### 投资研究
- **板块轮动分析**：识别当前热点板块和龙头股
- **异动监控**：发现超预期表现的个股
- **风险评估**：评估个股相对板块的偏离风险

### 量化策略
- **选股因子**：龙头得分可作为选股因子
- **择时信号**：异动检测可作为买卖信号
- **风控指标**：偏离度可作为风险控制指标

## 📁 项目结构

```
量化工具/
├── 龙头中军.py              # 板块龙头分析主程序
├── 严重异动计算.py          # 个股异动检测
├── 近期热点题材.py          # 热点题材挖掘
├── 近期没有回调完毕的股票.py # 回调分析
├── akshare_cache/           # 数据缓存目录
├── requirements.txt         # 依赖包列表
└── README.md               # 项目说明
```

## ⚠️ 免责声明

本工具仅供学习和研究使用，不构成投资建议。
- 股市有风险，投资需谨慎
- 历史数据不代表未来表现
- 请结合基本面分析做出投资决策

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发计划
- [ ] 添加更多技术指标
- [ ] 支持港股和美股
- [ ] 开发Web界面
- [ ] 增加回测功能

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

- 数据来源：[AKShare](https://github.com/akfamily/akshare)
- 感谢所有贡献者和用户的反馈

---

⭐ 如果这个项目对您有帮助，请给个Star支持一下！
