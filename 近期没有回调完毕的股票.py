import akshare as ak
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time

def get_industry_stocks(industry_name):
    """获取行业板块成分股（修复概念板块问题）"""
    # 获取所有行业板块
    industry_list = ak.stock_board_industry_name_em()
    
    # 查找目标行业ID
    industry_info = industry_list[industry_list['name'] == industry_name]
    if industry_info.empty:
        print(f"未找到行业板块: {industry_name}")
        return pd.DataFrame()
    
    industry_code = industry_info['code'].values[0]
    return ak.stock_board_industry_cons_em(symbol=industry_code)

def get_target_stocks(industry_name):
    """获取符合条件的目标股票（修复所有问题）"""
    # 1. 获取行业板块成分股（使用修复后的函数）
    industry_df = get_industry_stocks(industry_name)
    if industry_df.empty:
        return pd.DataFrame()
    
    # 准备结果存储
    results = []
    
    # 设置时间范围
    end_date = datetime.now().strftime('%Y%m%d')
    start_rise = (datetime.now() - timedelta(days=90)).strftime('%Y%m%d')
    start_fall = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
    
    # 2. 遍历每只股票
    for idx, row in industry_df.iterrows():
        symbol = row['代码']  # 格式如: 600000
        stock_name = row['名称']
        
        try:
            # 统一股票代码格式 (添加市场后缀)
            if symbol.startswith('6'):
                ts_code = f"{symbol}.SH"
            else:
                ts_code = f"{symbol}.SZ"
            
            print(f"正在处理: {stock_name}({symbol})...")
            
            # 获取历史行情 (使用后复权数据)
            stock_data = ak.stock_zh_a_hist(symbol=symbol, period="daily", 
                                          start_date=start_rise, end_date=end_date, 
                                          adjust="hfq")
            
            if stock_data.empty or len(stock_data) < 10:
                print(f"  -> 数据不足，跳过")
                continue
                
            # 计算涨幅/跌幅
            stock_data = stock_data.sort_values('日期')
            start_price = stock_data.iloc[0]['收盘']
            high_price = stock_data['最高'].max()
            current_price = stock_data.iloc[-1]['收盘']
            
            # 计算涨幅（期间最大涨幅）
            rise_ratio = (high_price - start_price) / start_price * 100
            
            # 计算从高点回落幅度
            high_date = stock_data[stock_data['最高'] == high_price].iloc[0]['日期']
            fall_period_data = stock_data[stock_data['日期'] >= high_date]
            
            if not fall_period_data.empty:
                fall_ratio = (current_price - high_price) / high_price * 100
                abs_fall_ratio = abs(fall_ratio)
            else:
                abs_fall_ratio = 0
            
            # 3. 获取公告数据 (修复字段名问题)
            time.sleep(1)  # 防止请求过快
            try:
                news_data = ak.stock_news_em(symbol=symbol)
            except Exception as e:
                print(f"  -> 获取新闻失败: {str(e)}")
                news_data = pd.DataFrame()
            
            # 处理股东减持公告
            has_reduction = False
            has_clarify = False
            
            if not news_data.empty:
                # 使用正确的日期字段名: '发布时间'
                recent_news = news_data[news_data['发布时间'] >= start_fall]
                
                # 检查减持公告
                reduction_titles = [t for t in recent_news['标题'].astype(str) if '减持' in t]
                has_reduction = len(reduction_titles) > 0
                
                # 检查澄清公告
                clarify_titles = [t for t in recent_news['标题'].astype(str) 
                                 if '澄清' in t or '说明' in t or '否认' in t]
                has_clarify = len(clarify_titles) > 0
            
            # 收集结果
            results.append({
                '股票代码': symbol,
                '股票名称': stock_name,
                '前期最大涨幅(%)': round(rise_ratio, 2),
                '高点回落幅度(%)': round(abs_fall_ratio, 2),
                '近期股东减持': has_reduction,
                '有澄清公告': has_clarify,
                '当前价格': current_price,
                '历史高点': high_price
            })
            
            print(f"  -> 涨幅: {rise_ratio:.2f}%, 回落: {abs_fall_ratio:.2f}%, "
                  f"减持: {has_reduction}, 澄清: {has_clarify}")
            
        except Exception as e:
            print(f"处理股票 {stock_name}({symbol}) 时出错: {str(e)}")
            continue
    
    # 转换为DataFrame
    result_df = pd.DataFrame(results)
    
    # 5. 筛选条件
    if not result_df.empty:
        target_df = result_df[
            (result_df['前期最大涨幅(%)'] > 100) &    # 前期涨幅>100%
            (result_df['高点回落幅度(%)'] > 30) &      # 从高点下跌>30%
            (result_df['近期股东减持']) &             # 存在减持公告
            (result_df['有澄清公告'])                # 有澄清公告
        ]
        return target_df
    else:
        return pd.DataFrame()

# 获取所有行业板块（真正的行业分类）
industry_list = ak.stock_board_industry_name_em()
print("可用行业板块:")
print(industry_list['name'].head(20))

# 示例：在"光伏设备"行业搜索目标股票
print("\n开始搜索目标股票...")
target_stocks = get_target_stocks(industry_name="光伏设备")

print("\n符合条件的股票：")
if not target_stocks.empty:
    print(target_stocks[['股票代码', '股票名称', '前期最大涨幅(%)', '高点回落幅度(%)']])
else:
    print("未找到符合条件的股票")